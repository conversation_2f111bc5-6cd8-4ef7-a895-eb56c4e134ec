{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 1 Assignment: Simple Data Analysis\n", "## Wholesale Customers Dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "df = pd.read_csv('Wholesale_Customers_Data.csv')\n", "print(\"Dataset loaded!\")\n", "print(f\"Shape: {df.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show first 5 rows\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing values:\")\n", "print(df.isnull().sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check data types\n", "print(\"Data types:\")\n", "print(df.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics\n", "df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simple Transformations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create new feature: Total Spending\n", "df['Total_Spending'] = df['Fresh'] + df['Milk'] + df['Grocery'] + df['Frozen'] + df['Detergents_Paper'] + df['Delicassen']\n", "print(\"New feature created: Total_Spending\")\n", "print(f\"Average total spending: {df['Total_Spending'].mean():.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualization 1: Distribution of Fresh Products"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(8, 5))\n", "plt.hist(df['Fresh'], bins=20, color='green', alpha=0.7)\n", "plt.title('Distribution of Fresh Product Spending')\n", "plt.xlabel('Fresh Product Spending')\n", "plt.ylabel('Number of Customers')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization 2: Fresh vs <PERSON> Spending"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(8, 6))\n", "plt.scatter(df['Fresh'], df['Milk'], alpha=0.6, color='blue')\n", "plt.title('Fresh vs Milk Product Spending')\n", "plt.xlabel('Fresh Product Spending')\n", "plt.ylabel('Milk Product Spending')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Visualization 3: Channel Distribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(6, 6))\n", "channel_counts = df['Channel'].value_counts()\n", "plt.pie(channel_counts.values, labels=['Retail', 'Hotel/Restaurant'], autopct='%1.1f%%')\n", "plt.title('Customer Channel Distribution')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Analysis Summary:\")\n", "print(f\"Total customers: {len(df)}\")\n", "print(f\"No missing values found\")\n", "print(f\"Average total spending: {df['Total_Spending'].mean():.0f}\")\n", "print(f\"Channel 1 customers: {(df['Channel'] == 1).sum()}\")\n", "print(f\"Channel 2 customers: {(df['Channel'] == 2).sum()}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}