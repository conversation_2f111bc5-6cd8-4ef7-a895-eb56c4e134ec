# Data Analysis Assignment - Wholesale Customers Dataset

## Overview
This repository contains a comprehensive data analysis project using the Wholesale Customers dataset. The analysis demonstrates data loading, cleaning, transformation, and visualization techniques using Python's pandas, matplotlib, and seaborn libraries.

## Dataset Information
The **Wholesale Customers Dataset** contains information about wholesale customers with the following features:
- **Channel**: Customer channel (1=Hotel/Restaurant/Cafe, 2=Retail)
- **Region**: Customer region (1-3)
- **Fresh**: Annual spending on fresh products
- **Milk**: Annual spending on milk products
- **Grocery**: Annual spending on grocery products
- **Frozen**: Annual spending on frozen products
- **Detergents_Paper**: Annual spending on detergents and paper products
- **Delicassen**: Annual spending on delicatessen products

**Dataset Size**: 440 customers × 8 features

## Project Structure
```
├── data_analysis_assignment.ipynb    # Main Jupyter notebook with analysis
├── Wholesale_Customers_Data.csv      # Dataset file
└── README.md                         # Project documentation
```

## Analysis Components

### 1. Data Loading and Exploration
- Loading the CSV dataset using pandas
- Examining dataset structure, shape, and basic statistics
- Displaying first few rows and column information

### 2. Data Cleaning
- Checking for missing values (none found)
- Identifying and handling duplicate records (none found)
- Verifying data types and conversions

### 3. Data Transformations
Four new features were created:
- **Total_Spending**: Sum of all product category spending
- **Customer_Type**: Descriptive labels for channel types
- **Spending_Category**: Categorization into Low/Medium/High based on quantiles
- **Fresh_Ratio**: Percentage of total spending on fresh products

### 4. Data Visualizations
The project includes multiple insightful visualizations:

#### Visualization 1: Distribution Analysis
- Histogram showing the distribution of total annual spending
- Box plot revealing spending patterns and outliers

#### Visualization 2: Relationship Analysis
- Scatter plot examining the relationship between Fresh and Milk product spending
- Color-coded by customer type with correlation coefficient

#### Visualization 3: Categorical Breakdown
- Bar charts showing average spending by customer type and region
- Pie chart displaying distribution of spending categories
- Fresh product ratio comparison across customer types

#### Bonus Visualization: Correlation Heatmap
- Heatmap showing correlations between all product categories
- Identifies which products are commonly purchased together

## Key Findings

1. **Customer Distribution**: The dataset contains both Hotel/Restaurant/Cafe and Retail customers
2. **Spending Patterns**: Wide variation in total spending across customers
3. **Product Relationships**: Correlation analysis reveals purchasing patterns between product categories
4. **Regional Differences**: Spending patterns vary across different regions
5. **Customer Segmentation**: Clear segmentation into low, medium, and high spending categories

## Requirements
```python
pandas
matplotlib
seaborn
numpy
jupyter
```

## Installation and Usage

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd assignment_1
   ```

2. **Install required packages**:
   ```bash
   pip install pandas matplotlib seaborn jupyter numpy
   ```

3. **Run the Jupyter notebook**:
   ```bash
   jupyter notebook data_analysis_assignment.ipynb
   ```

4. **Execute cells sequentially** to see the complete analysis

## Features Demonstrated

### Data Loading
- Reading CSV files with pandas
- Basic dataset exploration techniques

### Data Cleaning
- Missing value detection and handling
- Duplicate record identification
- Data type verification

### Data Transformation
- Feature engineering from existing columns
- Categorical variable creation
- Quantile-based categorization
- Ratio calculations

### Data Visualization
- Histograms and distribution plots
- Scatter plots with categorical coloring
- Bar charts and pie charts
- Correlation heatmaps
- Multi-subplot layouts

## Technical Approach
- **Simple and Clean Code**: No complex Python constructs used
- **Separate Outputs**: Each analysis component produces distinct outputs
- **Comprehensive Documentation**: Detailed explanations and insights for each step
- **Professional Visualizations**: Well-formatted plots with titles, labels, and legends

## Results
The analysis successfully demonstrates:
- Complete data pipeline from loading to visualization
- Meaningful feature engineering
- Multiple visualization techniques
- Clear insights and business understanding
- Professional documentation and code structure

## Author
Created as part of Day 1 Data Analysis Assignment

## License
This project is for educational purposes.
