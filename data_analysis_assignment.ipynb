{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 1 Assignment: Simple Data Analysis\n", "\n", "## Wholesale Customers Dataset Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "# Set style for better visualizations\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "df = pd.read_csv('Wholesale_Customers_Data.csv')\n", "\n", "print(\"Dataset loaded successfully!\")\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Number of rows: {df.shape[0]}\")\n", "print(f\"Number of columns: {df.shape[1]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "print(\"Column names:\")\n", "print(df.columns.tolist())\n", "print(\"\\nData types:\")\n", "print(df.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic statistics\n", "print(\"Basic statistical summary:\")\n", "df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Cleaning and Missing Value Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing values in each column:\")\n", "missing_values = df.isnull().sum()\n", "print(missing_values)\n", "\n", "print(f\"\\nTotal missing values: {missing_values.sum()}\")\n", "print(f\"Percentage of missing values: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for duplicate rows\n", "duplicates = df.duplicated().sum()\n", "print(f\"Number of duplicate rows: {duplicates}\")\n", "\n", "if duplicates > 0:\n", "    print(\"Removing duplicate rows...\")\n", "    df = df.drop_duplicates()\n", "    print(f\"Dataset shape after removing duplicates: {df.shape}\")\n", "else:\n", "    print(\"No duplicate rows found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check data types and convert if necessary\n", "print(\"Current data types:\")\n", "print(df.dtypes)\n", "\n", "# All columns are already in appropriate integer format\n", "print(\"\\nData types are appropriate - no conversion needed.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Transformations and Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a copy of the original dataset for transformations\n", "df_transformed = df.copy()\n", "\n", "print(\"Creating new features from existing data...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature 1: Total Annual Spending\n", "spending_columns = ['Fresh', 'Milk', 'Grocery', 'Frozen', 'Detergents_Paper', 'Delicassen']\n", "df_transformed['Total_Spending'] = df_transformed[spending_columns].sum(axis=1)\n", "\n", "print(\"Feature 1 created: Total_Spending\")\n", "print(f\"Total spending statistics:\")\n", "print(df_transformed['Total_Spending'].describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature 2: Customer Type based on Channel\n", "df_transformed['Customer_Type'] = df_transformed['Channel'].map({1: 'Hotel/Restaurant/Cafe', 2: 'Retail'})\n", "\n", "print(\"Feature 2 created: Customer_Type\")\n", "print(\"Customer type distribution:\")\n", "print(df_transformed['Customer_Type'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature 3: Spending Category (High, Medium, Low)\n", "# Using quantiles to categorize spending\n", "spending_quantiles = df_transformed['Total_Spending'].quantile([0.33, 0.67])\n", "\n", "def categorize_spending(total_spending):\n", "    if total_spending <= spending_quantiles[0.33]:\n", "        return 'Low'\n", "    elif total_spending <= spending_quantiles[0.67]:\n", "        return 'Medium'\n", "    else:\n", "        return 'High'\n", "\n", "df_transformed['Spending_Category'] = df_transformed['Total_Spending'].apply(categorize_spending)\n", "\n", "print(\"Feature 3 created: Spending_Category\")\n", "print(\"Spending category distribution:\")\n", "print(df_transformed['Spending_Category'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature 4: Fresh to Total Ratio (percentage of spending on fresh products)\n", "df_transformed['Fresh_Ratio'] = (df_transformed['Fresh'] / df_transformed['Total_Spending']) * 100\n", "\n", "print(\"Feature 4 created: Fresh_Ratio\")\n", "print(f\"Fresh ratio statistics:\")\n", "print(df_transformed['Fresh_Ratio'].describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display the transformed dataset\n", "print(\"Transformed dataset shape:\", df_transformed.shape)\n", "print(\"\\nNew columns added:\")\n", "new_columns = ['Total_Spending', 'Customer_Type', 'Spending_Category', 'Fresh_Ratio']\n", "print(new_columns)\n", "print(\"\\nFirst 5 rows with new features:\")\n", "df_transformed[['Channel', 'Region'] + new_columns].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization 1: Distribution of Total Spending"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization 1: Distribution of Total Spending\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Subplot 1: Histogram\n", "plt.subplot(1, 2, 1)\n", "plt.hist(df_transformed['Total_Spending'], bins=30, color='skyblue', alpha=0.7, edgecolor='black')\n", "plt.title('Distribution of Total Annual Spending', fontsize=14, fontweight='bold')\n", "plt.xlabel('Total Spending')\n", "plt.ylabel('Frequency')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Subplot 2: Box plot\n", "plt.subplot(1, 2, 2)\n", "plt.boxplot(df_transformed['Total_Spending'], patch_artist=True, \n", "            boxprops=dict(facecolor='lightcoral', alpha=0.7))\n", "plt.title('Box Plot of Total Annual Spending', fontsize=14, fontweight='bold')\n", "plt.ylabel('Total Spending')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Insight: The distribution shows the spread of customer spending patterns.\")\n", "print(f\"Mean spending: {df_transformed['Total_Spending'].mean():.2f}\")\n", "print(f\"Median spending: {df_transformed['Total_Spending'].median():.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization 2: Relationship between Fresh and Milk Spending"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization 2: Scatt<PERSON> plot showing relationship between Fresh and Milk spending\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create scatter plot with different colors for customer types\n", "for customer_type in df_transformed['Customer_Type'].unique():\n", "    data = df_transformed[df_transformed['Customer_Type'] == customer_type]\n", "    plt.scatter(data['Fresh'], data['Milk'], alpha=0.6, label=customer_type, s=50)\n", "\n", "plt.title('Relationship between Fresh and Milk Product Spending', fontsize=14, fontweight='bold')\n", "plt.xlabel('Fresh Product Spending')\n", "plt.ylabel('Milk Product Spending')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Add correlation coefficient\n", "correlation = df_transformed['Fresh'].corr(df_transformed['Milk'])\n", "plt.text(0.05, 0.95, f'Correlation: {correlation:.3f}', transform=plt.gca().transAxes, \n", "         bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Insight: The correlation between Fresh and Milk spending is {correlation:.3f}\")\n", "if abs(correlation) > 0.5:\n", "    print(\"This indicates a moderate to strong relationship.\")\n", "elif abs(correlation) > 0.3:\n", "    print(\"This indicates a weak to moderate relationship.\")\n", "else:\n", "    print(\"This indicates a weak relationship.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization 3: Categorical Breakdown - Spending by Customer Type and Region"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization 3: Bar plots showing spending patterns by categories\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Plot 1: Average spending by Customer Type\n", "customer_spending = df_transformed.groupby('Customer_Type')['Total_Spending'].mean()\n", "axes[0, 0].bar(customer_spending.index, customer_spending.values, \n", "               color=['lightblue', 'lightcoral'], alpha=0.8)\n", "axes[0, 0].set_title('Average Total Spending by Customer Type', fontweight='bold')\n", "axes[0, 0].set_ylabel('Average Total Spending')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for i, v in enumerate(customer_spending.values):\n", "    axes[0, 0].text(i, v + 1000, f'{v:.0f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 2: Spending distribution by Region\n", "region_spending = df_transformed.groupby('Region')['Total_Spending'].mean()\n", "axes[0, 1].bar(region_spending.index, region_spending.values, \n", "               color=['gold', 'lightgreen', 'plum'], alpha=0.8)\n", "axes[0, 1].set_title('Average Total Spending by Region', fontweight='bold')\n", "axes[0, 1].set_xlabel('Region')\n", "axes[0, 1].set_ylabel('Average Total Spending')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for i, v in enumerate(region_spending.values):\n", "    axes[0, 1].text(i, v + 1000, f'{v:.0f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 3: Count of customers by Spending Category\n", "spending_counts = df_transformed['Spending_Category'].value_counts()\n", "axes[1, 0].pie(spending_counts.values, labels=spending_counts.index, autopct='%1.1f%%', \n", "               colors=['lightcoral', 'gold', 'lightblue'], startangle=90)\n", "axes[1, 0].set_title('Distribution of Customers by Spending Category', fontweight='bold')\n", "\n", "# Plot 4: Fresh Ratio by Customer Type\n", "fresh_ratio_by_type = df_transformed.groupby('Customer_Type')['Fresh_Ratio'].mean()\n", "axes[1, 1].bar(fresh_ratio_by_type.index, fresh_ratio_by_type.values, \n", "               color=['lightsteelblue', 'lightsalmon'], alpha=0.8)\n", "axes[1, 1].set_title('Average Fresh Product Ratio by Customer Type', fontweight='bold')\n", "axes[1, 1].set_ylabel('Fresh Ratio (%)')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for i, v in enumerate(fresh_ratio_by_type.values):\n", "    axes[1, 1].text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Insights from categorical analysis:\")\n", "print(f\"1. {customer_spending.idxmax()} customers spend more on average ({customer_spending.max():.0f} vs {customer_spending.min():.0f})\")\n", "print(f\"2. Region {region_spending.idxmax()} has the highest average spending ({region_spending.max():.0f})\")\n", "print(f\"3. Fresh products represent {fresh_ratio_by_type.mean():.1f}% of total spending on average\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Additional Visualization: Heatmap of Product Category Correlations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bonus Visualization: Correlation heatmap of product categories\n", "plt.figure(figsize=(10, 8))\n", "\n", "# Calculate correlation matrix for product spending categories\n", "product_columns = ['Fresh', 'Milk', 'Grocery', 'Frozen', 'Detergents_Paper', 'Delicassen']\n", "correlation_matrix = df_transformed[product_columns].corr()\n", "\n", "# Create heatmap\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, fmt='.3f', cbar_kws={'shrink': 0.8})\n", "plt.title('Correlation Heatmap of Product Categories', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Insight: The heatmap shows which product categories tend to be purchased together.\")\n", "print(\"Strong positive correlations (close to 1) indicate products often bought together.\")\n", "print(\"Strong negative correlations (close to -1) indicate products rarely bought together.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Summary and Conclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary statistics and key findings\n", "print(\"=\" * 60)\n", "print(\"SUMMARY OF DATA ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n1. DATASET OVERVIEW:\")\n", "print(f\"   - Total customers: {df.shape[0]}\")\n", "print(f\"   - Product categories: {len(product_columns)}\")\n", "print(f\"   - No missing values found\")\n", "print(f\"   - No duplicate records found\")\n", "\n", "print(f\"\\n2. CUSTOMER DISTRIBUTION:\")\n", "customer_dist = df_transformed['Customer_Type'].value_counts()\n", "for customer_type, count in customer_dist.items():\n", "    percentage = (count / len(df_transformed)) * 100\n", "    print(f\"   - {customer_type}: {count} customers ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n3. SPENDING PATTERNS:\")\n", "print(f\"   - Average total spending: {df_transformed['Total_Spending'].mean():.0f}\")\n", "print(f\"   - Median total spending: {df_transformed['Total_Spending'].median():.0f}\")\n", "print(f\"   - Spending range: {df_transformed['Total_Spending'].min():.0f} - {df_transformed['Total_Spending'].max():.0f}\")\n", "\n", "print(f\"\\n4. KEY INSIGHTS:\")\n", "print(f\"   - Most customers fall into the {df_transformed['Spending_Category'].mode()[0]} spending category\")\n", "print(f\"   - Fresh products account for {df_transformed['Fresh_Ratio'].mean():.1f}% of total spending on average\")\n", "print(f\"   - {customer_spending.idxmax()} customers tend to spend more than {customer_spending.idxmin()} customers\")\n", "\n", "print(f\"\\n5. FEATURES CREATED:\")\n", "print(f\"   - Total_Spending: Sum of all product category spending\")\n", "print(f\"   - Customer_Type: Descriptive labels for channel types\")\n", "print(f\"   - Spending_Category: Low/Medium/High based on quantiles\")\n", "print(f\"   - Fresh_Ratio: Percentage of spending on fresh products\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"ANALYSIS COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}