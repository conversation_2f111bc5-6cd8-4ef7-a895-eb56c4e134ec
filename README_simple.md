# Simple Data Analysis Assignment

## Files
- `simple_data_analysis.ipynb` - Main notebook
- `Wholesale_Customers_Data.csv` - Dataset
- `README_simple.md` - This file

## What's Done
1. **Data Loading** - Load CSV with pandas
2. **Data Cleaning** - Check missing values and data types  
3. **Simple Transformation** - Create Total_Spending column
4. **3 Visualizations**:
   - Histogram of Fresh product spending
   - Scatter plot of Fresh vs Milk spending
   - Pie chart of customer channels

## How to Run
1. Install: `pip install pandas matplotlib seaborn jupyter`
2. Run: `jupyter notebook simple_data_analysis.ipynb`
3. Execute all cells

## Dataset
- 440 customers
- 8 columns (Channel, Region, Fresh, Milk, Grocery, Frozen, Detergents_Paper, Delicassen)
- No missing values
